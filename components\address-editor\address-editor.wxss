.address-editor {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.address-editor.show {
  opacity: 1;
  visibility: visible;
}

.editor-content {
  width: 90%;
  max-width: 500px;
  max-height: 80%;
  background-color: #fff;
  border-radius: 12px;
  overflow: hidden;
  transform: translateY(20px);
  transition: transform 0.3s ease;
}

.address-editor.show .editor-content {
  transform: translateY(0);
}

/* 标题栏 */
.editor-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px;
  background-color: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.header-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.close-btn {
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: #666;
  background-color: #f0f0f0;
  border-radius: 50%;
}

/* 表单内容 */
.editor-body {
  padding: 20px;
  max-height: 400px;
  overflow-y: auto;
}

.form-item {
  margin-bottom: 20px;
}

.form-label {
  display: block;
  font-size: 14px;
  color: #333;
  margin-bottom: 8px;
  font-weight: 500;
}

.form-label.required::after {
  content: '*';
  color: #ff4757;
  margin-left: 4px;
}

.form-input {
  width: 100%;
  height: 44px;
  padding: 0 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
  background-color: #fff;
  box-sizing: border-box;
}

.form-input:focus {
  border-color: #007aff;
  outline: none;
}

.char-count {
  font-size: 12px;
  color: #999;
  text-align: right;
  margin-top: 4px;
  display: block;
}

.address-input-row {
  display: flex;
  align-items: center;
  gap: 10px;
}

.address-input {
  flex: 1;
}

.search-btn {
  width: 60px;
  height: 44px;
  background-color: #007aff;
  color: #fff;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.search-btn:active {
  background-color: #0056b3;
}

/* 位置选择区域 */
.location-section {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #e9ecef;
}

.section-title {
  display: block;
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 15px;
}

.location-buttons {
  display: flex;
  gap: 10px;
  margin-bottom: 15px;
}

.location-btn {
  flex: 1;
  height: 44px;
  background-color: #f8f9fa;
  color: #333;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.location-btn:active {
  background-color: #e9ecef;
}

.location-btn.loading {
  background-color: #e9ecef;
  color: #666;
}

.location-info {
  background-color: #f8f9fa;
  padding: 12px;
  border-radius: 6px;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.location-text {
  font-size: 12px;
  color: #666;
}

/* 操作按钮 */
.editor-footer {
  display: flex;
  padding: 20px;
  gap: 15px;
  background-color: #f8f9fa;
  border-top: 1px solid #e9ecef;
}

.cancel-btn,
.confirm-btn {
  flex: 1;
  height: 44px;
  border-radius: 6px;
  font-size: 16px;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
}

.cancel-btn {
  background-color: #f8f9fa;
  color: #666;
  border: 1px solid #ddd;
}

.cancel-btn:active {
  background-color: #e9ecef;
}

.confirm-btn {
  background-color: #007aff;
  color: #fff;
}

.confirm-btn:active {
  background-color: #0056b3;
}
